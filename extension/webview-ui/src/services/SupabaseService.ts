import { vscode } from '../utilities/vscode';

// Types for user data
export interface UserProfile {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
  provider?: string;
}

export interface SubscriptionData {
  id: string;
  plan: string;
  status: 'active' | 'inactive' | 'cancelled';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  quotas: {
    transcriptionMinutes: number;
    optimizationTokens: number;
    usedTranscriptionMinutes: number;
    usedOptimizationTokens: number;
  };
}

export interface CreditsData {
  balance: number;
  lastUpdated: string;
}

export interface UsageData {
  transcriptionMinutes: number;
  optimizationTokens: number;
  lastActivity: string;
}

export interface RecentActivity {
  id: string;
  type: 'transcription' | 'optimization';
  timestamp: string;
  details: {
    model?: string;
    service?: string;
    duration?: number;
    tokens?: number;
  };
}

/**
 * Service for fetching user data from Supabase
 * Communicates with the extension host to get authenticated data
 */
export class SupabaseService {
  private static instance: SupabaseService;

  private constructor() {}

  public static getInstance(): SupabaseService {
    if (!SupabaseService.instance) {
      SupabaseService.instance = new SupabaseService();
    }
    return SupabaseService.instance;
  }

  /**
   * Get user profile information
   */
  async getUserProfile(): Promise<UserProfile | null> {
    try {
      const response = await this.sendMessage('getUserProfile');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
      return null;
    }
  }

  /**
   * Get user subscription data
   */
  async getSubscriptionData(): Promise<SubscriptionData | null> {
    try {
      const response = await this.sendMessage('getSubscriptionData');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch subscription data:', error);
      return null;
    }
  }

  /**
   * Get user credits balance
   */
  async getCreditsData(): Promise<CreditsData | null> {
    try {
      const response = await this.sendMessage('getCreditsData');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch credits data:', error);
      return null;
    }
  }

  /**
   * Get usage statistics
   */
  async getUsageData(): Promise<UsageData | null> {
    try {
      const response = await this.sendMessage('getUsageData');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch usage data:', error);
      return null;
    }
  }

  /**
   * Get recent activity
   */
  async getRecentActivity(limit: number = 10): Promise<RecentActivity[]> {
    try {
      const response = await this.sendMessage('getRecentActivity', { limit });
      return response.data || [];
    } catch (error) {
      console.error('Failed to fetch recent activity:', error);
      return [];
    }
  }

  /**
   * Sign out user (clears both Supabase session and API key)
   */
  async signOut(): Promise<void> {
    try {
      await this.sendMessage('signOut');
    } catch (error) {
      console.error('Failed to sign out:', error);
      throw error;
    }
  }

  /**
   * Send message to extension host and wait for response
   */
  private async sendMessage(command: string, data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const messageId = `${command}-${Date.now()}-${Math.random()}`;

      // Set up response listener
      const handleMessage = (event: MessageEvent) => {
        const message = event.data;
        if (message.id === messageId) {
          window.removeEventListener('message', handleMessage);
          if (message.error) {
            reject(new Error(message.error));
          } else {
            resolve(message);
          }
        }
      };

      window.addEventListener('message', handleMessage);

      // Send message to extension host
      vscode.postMessage({
        command,
        id: messageId,
        data
      });

      // Timeout after 10 seconds
      setTimeout(() => {
        window.removeEventListener('message', handleMessage);
        reject(new Error(`Request timeout for ${command}`));
      }, 10000);
    });
  }
}

export default SupabaseService;