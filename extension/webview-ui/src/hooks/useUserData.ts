import { useState, useEffect, useCallback } from 'react';
import SupabaseService, { 
  UserProfile, 
  SubscriptionData, 
  CreditsData, 
  UsageData, 
  RecentActivity 
} from '../services/SupabaseService';

interface UserDataState {
  profile: UserProfile | null;
  subscription: SubscriptionData | null;
  credits: CreditsData | null;
  usage: UsageData | null;
  recentActivity: RecentActivity[];
  isLoading: boolean;
  error: string | null;
}

interface UseUserDataReturn extends UserDataState {
  refreshData: () => Promise<void>;
  signOut: () => Promise<void>;
}

/**
 * Custom hook for managing user data from Supabase
 * Handles loading states, error handling, and data refreshing
 */
export const useUserData = (isAuthenticated: boolean): UseUserDataReturn => {
  const [state, setState] = useState<UserDataState>({
    profile: null,
    subscription: null,
    credits: null,
    usage: null,
    recentActivity: [],
    isLoading: false,
    error: null
  });

  const supabaseService = SupabaseService.getInstance();

  /**
   * Fetch all user data
   */
  const fetchUserData = useCallback(async () => {
    if (!isAuthenticated) {
      setState(prev => ({
        ...prev,
        profile: null,
        subscription: null,
        credits: null,
        usage: null,
        recentActivity: [],
        isLoading: false,
        error: null
      }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Fetch all data in parallel
      const [profile, subscription, credits, usage, recentActivity] = await Promise.all([
        supabaseService.getUserProfile(),
        supabaseService.getSubscriptionData(),
        supabaseService.getCreditsData(),
        supabaseService.getUsageData(),
        supabaseService.getRecentActivity(5)
      ]);

      setState(prev => ({
        ...prev,
        profile,
        subscription,
        credits,
        usage,
        recentActivity,
        isLoading: false,
        error: null
      }));
    } catch (error) {
      console.error('Failed to fetch user data:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch user data'
      }));
    }
  }, [isAuthenticated, supabaseService]);

  /**
   * Refresh user data
   */
  const refreshData = useCallback(async () => {
    await fetchUserData();
  }, [fetchUserData]);

  /**
   * Sign out user
   */
  const signOut = useCallback(async () => {
    try {
      await supabaseService.signOut();
      setState(prev => ({
        ...prev,
        profile: null,
        subscription: null,
        credits: null,
        usage: null,
        recentActivity: [],
        error: null
      }));
    } catch (error) {
      console.error('Failed to sign out:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to sign out'
      }));
      throw error;
    }
  }, [supabaseService]);

  // Fetch data when authentication status changes
  useEffect(() => {
    fetchUserData();
  }, [fetchUserData]);

  return {
    ...state,
    refreshData,
    signOut
  };
};

export default useUserData;
