import React from 'react';
import { UserProfile, SubscriptionData, CreditsData, RecentActivity } from '../services/SupabaseService';
import SubscriptionCard from './SubscriptionCard';
import CreditsCard from './CreditsCard';

interface ProfilePageProps {
  profile: UserProfile | null;
  subscription: SubscriptionData | null;
  credits: CreditsData | null;
  recentActivity: RecentActivity[];
  isLoading: boolean;
  onBack: () => void;
  onSignOut: () => void;
}

/**
 * Full profile page component with user details, subscription, and credits
 * Matches VS Code extension theme with dark/light mode support
 */
const ProfilePage: React.FC<ProfilePageProps> = ({
  profile,
  subscription,
  credits,
  recentActivity,
  isLoading,
  onBack,
  onSignOut
}) => {
  // Generate initials from name or email
  const getInitials = (name?: string, email?: string): string => {
    if (name) {
      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email.charAt(0).toUpperCase();
    }
    return 'U';
  };

  const initials = profile ? getInitials(profile.fullName, profile.email) : 'U';
  const displayName = profile?.fullName || profile?.email?.split('@')[0] || 'User';

  return (
    <div className="p-4 space-y-4">
      {/* Header with Back Button */}
      <div className="flex items-center gap-3 mb-4">
        <button
          onClick={onBack}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50"
        >
          <svg
            className="w-4 h-4 text-gray-600 dark:text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Profile
        </h2>
      </div>

      {/* Profile Info */}
      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-4">
          {/* Large Avatar */}
          <div className="relative w-16 h-16 flex-shrink-0">
            {profile?.avatarUrl ? (
              <img
                src={profile.avatarUrl}
                alt={`${displayName}'s avatar`}
                className="w-16 h-16 rounded-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) {
                    fallback.style.display = 'flex';
                  }
                }}
              />
            ) : null}
            <div
              className={`w-16 h-16 rounded-full bg-primary dark:bg-dark-primary text-black dark:text-white text-xl font-semibold flex items-center justify-center ${
                profile?.avatarUrl ? 'hidden' : 'flex'
              }`}
            >
              {initials}
            </div>
          </div>

          {/* User Details */}
          <div className="flex-1 min-w-0">
            {isLoading ? (
              <div className="space-y-2">
                <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-2/3"></div>
              </div>
            ) : (
              <>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
                  {displayName}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {profile?.email}
                </p>
                {profile?.provider && (
                  <p className="text-xs text-gray-400 dark:text-gray-500 capitalize">
                    Signed in with {profile.provider}
                  </p>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Subscription Card */}
      <SubscriptionCard subscription={subscription} isLoading={isLoading} />

      {/* Credits Card */}
      <CreditsCard credits={credits} isLoading={isLoading} />

      {/* Recent Activity */}
      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3">
          Recent Activity
        </h3>
        {isLoading ? (
          <div className="space-y-2">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            ))}
          </div>
        ) : recentActivity.length > 0 ? (
          <div className="space-y-2">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between text-xs">
                <span className="text-gray-700 dark:text-gray-300 capitalize">
                  {activity.type}
                  {activity.details.model && ` (${activity.details.model})`}
                </span>
                <span className="text-gray-500 dark:text-gray-400">
                  {new Date(activity.timestamp).toLocaleDateString()}
                </span>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-sm text-gray-500 dark:text-gray-400">
            No recent activity
          </p>
        )}
      </div>

      {/* Sign Out Button */}
      <button
        onClick={onSignOut}
        className="w-full py-2 px-3 bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-red-500/50"
      >
        Sign Out
      </button>
    </div>
  );
};

export default ProfilePage;
