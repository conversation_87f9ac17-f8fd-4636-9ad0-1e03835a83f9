import React from 'react';
import { SubscriptionData } from '../services/SupabaseService';

interface SubscriptionCardProps {
  subscription: SubscriptionData | null;
  isLoading: boolean;
}

/**
 * Subscription card component showing plan details and usage quotas
 * Matches VS Code extension theme with progress bars
 */
const SubscriptionCard: React.FC<SubscriptionCardProps> = ({
  subscription,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
        <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded animate-pulse mb-3"></div>
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Subscription
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          No active subscription
        </p>
      </div>
    );
  }

  // Calculate usage percentages
  const transcriptionPercentage = subscription.quotas.transcriptionMinutes > 0
    ? Math.min((subscription.quotas.usedTranscriptionMinutes / subscription.quotas.transcriptionMinutes) * 100, 100)
    : 0;

  const optimizationPercentage = subscription.quotas.optimizationTokens > 0
    ? Math.min((subscription.quotas.usedOptimizationTokens / subscription.quotas.optimizationTokens) * 100, 100)
    : 0;

  // Format token counts with K suffix
  const formatTokens = (tokens: number): string => {
    if (tokens >= 1000) {
      return `${(tokens / 1000).toFixed(0)}K`;
    }
    return tokens.toString();
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 dark:text-green-400';
      case 'inactive':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'cancelled':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
          Subscription
        </h3>
        <span className={`text-xs font-medium capitalize ${getStatusColor(subscription.status)}`}>
          {subscription.status}
        </span>
      </div>

      {/* Plan */}
      <div className="mb-4">
        <p className="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
          {subscription.plan} Plan
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-400">
          Until {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
        </p>
      </div>

      {/* Usage Quotas */}
      <div className="space-y-3">
        {/* Transcription Minutes */}
        <div>
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              Transcription
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {subscription.quotas.usedTranscriptionMinutes} / {subscription.quotas.transcriptionMinutes} min
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-primary dark:bg-dark-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${transcriptionPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Optimization Tokens */}
        <div>
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              Optimization
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              <span className="text-green-600 dark:text-green-400">
                {formatTokens(subscription.quotas.usedOptimizationTokens)}
              </span>
              {' / '}
              <span className="text-green-600 dark:text-green-400">
                {formatTokens(subscription.quotas.optimizationTokens)}
              </span>
              {' tokens'}
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-primary dark:bg-dark-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${optimizationPercentage}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionCard;
