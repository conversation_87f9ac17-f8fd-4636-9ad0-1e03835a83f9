import React from 'react';
import { UserProfile } from '../services/SupabaseService';

interface ProfileHeaderProps {
  profile: UserProfile | null;
  isLoading: boolean;
  onProfileClick: () => void;
}

/**
 * Profile header component showing user avatar and name
 * Matches VS Code extension theme with dark/light mode support
 */
const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  profile,
  isLoading,
  onProfileClick
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full animate-pulse"></div>
        <div className="flex-1">
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse mb-1"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-2/3"></div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return null;
  }

  // Generate initials from name or email
  const getInitials = (name?: string, email?: string): string => {
    if (name) {
      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email.charAt(0).toUpperCase();
    }
    return 'U';
  };

  const initials = getInitials(profile.fullName, profile.email);
  const displayName = profile.fullName || profile.email?.split('@')[0] || 'User';

  return (
    <button
      onClick={onProfileClick}
      className="w-full flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50"
    >
      {/* Avatar */}
      <div className="relative w-8 h-8 flex-shrink-0">
        {profile.avatarUrl ? (
          <img
            src={profile.avatarUrl}
            alt={`${displayName}'s avatar`}
            className="w-8 h-8 rounded-full object-cover"
            onError={(e) => {
              // Fallback to initials if image fails to load
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const fallback = target.nextElementSibling as HTMLElement;
              if (fallback) {
                fallback.style.display = 'flex';
              }
            }}
          />
        ) : null}
        <div
          className={`w-8 h-8 rounded-full bg-primary dark:bg-dark-primary text-black dark:text-white text-sm font-semibold flex items-center justify-center ${
            profile.avatarUrl ? 'hidden' : 'flex'
          }`}
        >
          {initials}
        </div>
      </div>

      {/* User Info */}
      <div className="flex-1 text-left min-w-0">
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
          {displayName}
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
          {profile.email}
        </div>
      </div>

      {/* Arrow Icon */}
      <div className="flex-shrink-0 text-gray-400 dark:text-gray-500">
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      </div>
    </button>
  );
};

export default ProfileHeader;
