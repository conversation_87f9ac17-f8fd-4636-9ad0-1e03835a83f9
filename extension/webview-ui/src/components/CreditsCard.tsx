import React from 'react';
import { CreditsData } from '../services/SupabaseService';

interface CreditsCardProps {
  credits: CreditsData | null;
  isLoading: boolean;
}

/**
 * Credits card component showing balance and add credits option
 * Matches VS Code extension theme
 */
const CreditsCard: React.FC<CreditsCardProps> = ({
  credits,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
        <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded animate-pulse mb-3"></div>
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-3"></div>
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      </div>
    );
  }

  const formatCredits = (amount: number): string => {
    return `$${amount.toFixed(2)}`;
  };

  const handleAddCredits = () => {
    // Open VoiceHype website billing page in external browser
    // This will be handled by the extension host
    window.postMessage({
      command: 'openExternalUrl',
      url: 'https://voicehype.ai/billing'
    }, '*');
  };

  return (
    <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
          Credits
        </h3>
        {credits && (
          <span className="text-xs text-gray-500 dark:text-gray-400">
            Updated {new Date(credits.lastUpdated).toLocaleDateString()}
          </span>
        )}
      </div>

      {/* Balance */}
      <div className="mb-4">
        {credits ? (
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {formatCredits(credits.balance)}
          </div>
        ) : (
          <div className="text-2xl font-bold text-gray-500 dark:text-gray-400">
            $0.00
          </div>
        )}
        <p className="text-xs text-gray-500 dark:text-gray-400">
          Available balance
        </p>
      </div>

      {/* Add Credits Button */}
      <button
        onClick={handleAddCredits}
        className="w-full py-2 px-3 bg-primary dark:bg-dark-primary hover:bg-primary/90 dark:hover:bg-dark-primary/90 text-black dark:text-white text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50"
      >
        Add Credits
      </button>
    </div>
  );
};

export default CreditsCard;
