import * as vscode from 'vscode';
import { randomBytes } from 'crypto';
import { URL } from 'url';
import { SecretsService } from './SecretsService';
import { IConfigurationService } from '../models/interfaces';
import { createClient, SupabaseClient, Session } from '@supabase/supabase-js';

/**
 * Authentication states for OAuth flow
 */
export enum AuthState {
  SignedOut = 'signedOut',
  SigningIn = 'signingIn',
  SignedIn = 'signedIn'
}

/**
 * User profile information from the authenticated session
 */
export interface UserProfile {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
}

/**
 * Supabase session information
 */
export interface SupabaseSession {
  access_token: string;
  refresh_token: string;
  token_type?: string;
  expires_at?: string;
  expires_in?: string;
  provider_token?: string;
  provider_refresh_token?: string;
}

/**
 * Service that handles authentication flows for the VS Code extension
 * with VoiceHype's Supabase backend.
 */
export class AuthenticationService implements vscode.Disposable {
  private _authState: AuthState = AuthState.SignedOut;
  private _userProfile: UserProfile | undefined;
  private _authStateChangeEmitter = new vscode.EventEmitter<AuthState>();
  private _disposables: vscode.Disposable[] = [];
  private _pendingStates = new Map<string, { resolve: (value: any) => void, reject: (reason: any) => void }>();
  private _supabaseUrl: string;

  private _supabaseSession: SupabaseSession | undefined;
  private _supabaseClient: SupabaseClient | undefined;

  public readonly onDidChangeAuthState = this._authStateChangeEmitter.event;
  
  constructor(
    private readonly _context: vscode.ExtensionContext,
    private readonly _secretsService: SecretsService,
    private readonly _configService: IConfigurationService
  ) {
    // Get Supabase URL from environment or use production URL
    this._supabaseUrl = 'https://supabase.voicehype.ai';
    
    // Register callback handler for the authentication flow
    this._disposables.push(
      vscode.window.registerUriHandler({
        handleUri: async (uri: vscode.Uri) => {
          await this._handleAuthRedirect(uri);
        }
      })
    );
    
    // Check existing session on startup
    this._checkExistingSession();
  }
  
  /**
   * Gets the current authentication state
   */
  public getAuthState(): AuthState {
    return this._authState;
  }
  
  /**
   * Gets the current user profile if authenticated
   */
  public getUserProfile(): UserProfile | undefined {
    return this._userProfile;
  }
    /**
   * Starts the sign-in process using the browser authentication flow
   */
  public async signIn(): Promise<void> {
    if (this._authState === AuthState.SigningIn) {
      return;
    }
    
    this._setAuthState(AuthState.SigningIn);
    
    try {
      // Generate state and verifier for PKCE
      const state = this._generateRandomState();
      
      // Create the auth URL with the extension callback
      const callbackUri = await this._getCallbackUri();
      const authUrl = this._buildAuthUrl(state, callbackUri);
      
      // Open the auth URL directly in browser without URI handling
      // Encode the URL to ensure clean handling across platforms
      const encodedUrl = encodeURI(authUrl);
      const browserOpened = await vscode.env.openExternal(vscode.Uri.parse(encodedUrl, true));
      
      // If user cancelled at this point, throw error
      if (!browserOpened) {
        throw new Error('Browser opening was cancelled');
      }
      
      // Add debug logging for URL handling
      console.log('Opened auth URL:', encodedUrl);
      
      // Wait for the redirect to complete with a Promise
      const result = await this._createAuthPromise(state);
      
      // Process successful authentication
      if (result && result.apiKey) {
        // Store the API key in the secure storage
        await this._secretsService.storeApiKey(result.apiKey);
        
        // Update the user profile
        this._userProfile = {
          id: result.userId,
          email: result.email,
          fullName: result.fullName,
          avatarUrl: result.avatarUrl
        };
        
        // Update auth state to signed in
        this._setAuthState(AuthState.SignedIn);
      } else {
        throw new Error('Failed to authenticate: No API key returned');
      }
    } catch (error) {
      console.error('Authentication error:', error);
      this._setAuthState(AuthState.SignedOut);
      throw error;
    }
  }
  
  /**
   * Signs out the current user
   */
  public async signOut(): Promise<void> {
    // Delete the API key from secure storage
    await this._secretsService.deleteApiKey();
    
    // Clear user profile
    this._userProfile = undefined;
    
    // Set state to signed out
    this._setAuthState(AuthState.SignedOut);
  }
  
  /**
   * Checks if the current API key is valid
   */
  public async validateApiKey(): Promise<boolean> {
    const apiKey = await this._secretsService.getApiKey();
    
    if (!apiKey) {
      return false;
    }
    
    try {
      // Call the Supabase function to validate the API key
      const response = await fetch(`${this._supabaseUrl}/functions/v1/validate-api-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        }
      });
      
      if (!response.ok) {
        // API key is invalid
        return false;
      }
      
      const data = await response.json();
      
      if (data.valid) {
        // If we have user data, update the profile
        if (data.user) {
          this._userProfile = {
            id: data.user.id,
            email: data.user.email,
            fullName: data.user.full_name,
            avatarUrl: data.user.avatar_url
          };
          
          this._setAuthState(AuthState.SignedIn);
        }
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error validating API key:', error);
      return false;
    }
  }
  
  /**
   * Handles the authentication redirect from the browser
   */
  private async _handleAuthRedirect(uri: vscode.Uri): Promise<void> {
    const query = new URLSearchParams(uri.query);
    const state = query.get('state');
    const apiKey = query.get('api_key');
    const error = query.get('error');
    
    if (!state) {
      console.error('No state parameter in the redirect URI');
      vscode.window.showErrorMessage('Authentication failed: Invalid state');
      return;
    }
    
    const pendingAuth = this._pendingStates.get(state);
    if (!pendingAuth) {
      console.error('No pending authentication found for state:', state);
      vscode.window.showErrorMessage('Authentication failed: Invalid state');
      return;
    }
    
    // Remove the pending state
    this._pendingStates.delete(state);
    
    if (error) {
      pendingAuth.reject(new Error(`Authentication error: ${error}`));
      return;
    }
    
    if (!apiKey) {
      pendingAuth.reject(new Error('No API key returned from authentication'));
      return;
    }
    
    // Get user information from the query params
    const userId = query.get('user_id') || '';
    const email = query.get('email') || '';
    const fullName = query.get('full_name') || undefined;
    const avatarUrl = query.get('avatar_url') || undefined;

    // Extract Supabase session tokens if present
    const access_token = query.get('access_token') || '';
    const refresh_token = query.get('refresh_token') || '';
    const token_type = query.get('token_type') || undefined;
    const expires_at = query.get('expires_at') || undefined;
    const expires_in = query.get('expires_in') || undefined;
    const provider_token = query.get('provider_token') || undefined;
    const provider_refresh_token = query.get('provider_refresh_token') || undefined;

    let supabaseSession: SupabaseSession | undefined = undefined;
    if (access_token && refresh_token) {
      supabaseSession = {
        access_token,
        refresh_token,
        token_type,
        expires_at,
        expires_in,
        provider_token,
        provider_refresh_token
      };
      // Store session securely
      await this._secretsService.storeSupabaseSession(JSON.stringify(supabaseSession));
      this._supabaseSession = supabaseSession;
      this._initSupabaseClient();
    } else {
      // Remove any previous session if not present
      await this._secretsService.deleteSupabaseSession();
      this._supabaseSession = undefined;
      this._supabaseClient = undefined;
    }

    // Resolve the authentication promise
    pendingAuth.resolve({
      apiKey,
      userId,
      email,
      fullName,
      avatarUrl,
      supabaseSession
    });
  }

  /**
   * Initialize Supabase client with the current session
   */
  private _initSupabaseClient() {
    if (!this._supabaseSession) {
      return;
    }
    this._supabaseClient = createClient(
      this._supabaseUrl,
      this._supabaseSession.access_token,
      {
        global: {
          headers: {
            Authorization: `Bearer ${this._supabaseSession.access_token}`
          }
        }
      }
    );
  }

  /**
   * Load Supabase session from storage on startup
   */
  private async _loadSupabaseSession() {
    const sessionStr = await this._secretsService.getSupabaseSession();
    if (sessionStr) {
      try {
        this._supabaseSession = JSON.parse(sessionStr);
        this._initSupabaseClient();
      } catch (e) {
        this._supabaseSession = undefined;
        this._supabaseClient = undefined;
      }
    }
  }

  /**
   * Gets the VS Code product name to respect different variants like Cursor, VSCodium, etc.
   */
  private _getVSCodeProductName(): string {
    // Get the product name from the environment
    const appName = vscode.env.appName || 'Visual Studio Code';
    
    // Add detailed logging to see exactly what appName contains
    console.log(`VS Code app name from environment: "${appName}"`);
    
    // Get a clean product name by removing version info (e.g., "Cursor 0.20.1" → "Cursor")
    const productName = appName.split(' ').filter(part => !part.match(/^\d+/)).join(' ');
    console.log(`Parsed product name: "${productName}"`);
    
    return productName;
  }
  
  /**
   * Gets the URI scheme for the current VS Code variant
   */
  private _getUriScheme(): string {
    const appName = vscode.env.appName || 'Visual Studio Code';
    
    // Map common VS Code variants to their URI schemes
    if (appName.toLowerCase().includes('cursor')) {
      return 'cursor';
    } else if (appName.toLowerCase().includes('vscodium')) {
      return 'vscodium';
    } else if (appName.toLowerCase().includes('windsurf')) {
      return 'windsurf';
    }
    
    // Default to 'vscode' for Visual Studio Code
    return 'vscode';
  }

  /**
   * Builds the authentication URL for the browser sign-in
   */
  private _buildAuthUrl(state: string, callbackUri: string): string {
    const params = new URLSearchParams();
    params.append('state', state);
    params.append('redirect_uri', callbackUri);
    
    // Add the VS Code product name to respect different variants
    const productName = this._getVSCodeProductName();
    params.append('product_name', productName);
    
    // Also add the actual scheme being used in the callback URI
    const scheme = callbackUri.split(':')[0];
    params.append('uri_scheme', scheme);
    
    // Ensure we're building a clean HTTP URL
    if (!this._supabaseUrl.startsWith('https://')) {
      throw new Error('Invalid Supabase URL - must use HTTPS');
    }
    
    console.log(`Building auth URL for product: "${productName}" with scheme: "${scheme}"`);
    
    // Use your existing VoiceHype website for authentication
    const authUrl = `https://voicehype.ai/vscode-auth?${params.toString()}`;
    console.log(`Complete auth URL: ${authUrl}`);
    
    return authUrl;
  }
  
  /**
   * Gets the callback URI for the extension
   */
  private async _getCallbackUri(): Promise<string> {
    // Get the extension ID from the publisher and name
    const extensionId = 'VoiceHype.voicehype';
    
    // Determine the correct URI scheme based on the product name
    const appName = vscode.env.appName || 'Visual Studio Code';
    console.log(`Getting callback URI for app: "${appName}"`);
    
    // Extract scheme based on product
    let scheme = 'vscode';
    
    // Map common VS Code variants to their URI schemes
    if (appName.toLowerCase().includes('cursor')) {
      scheme = 'cursor';
    } else if (appName.toLowerCase().includes('vscodium')) {
      scheme = 'vscodium';
    } else if (appName.toLowerCase().includes('windsurf')) {
      scheme = 'windsurf';
    }
    // Add more mappings as needed for other VS Code variants
    
    console.log(`Using URI scheme: "${scheme}" for product: "${appName}"`);
    
    // Create the callback URI with the appropriate scheme
    return `${scheme}://${extensionId}/auth-callback`;
  }
  
  /**
   * Generates a random state string for the OAuth flow
   */
  private _generateRandomState(): string {
    return randomBytes(32).toString('hex');
  }
  
  /**
   * Creates a promise that will be resolved when the authentication is complete
   */ /**
   * Creates a promise that resolves when the authentication is complete
   * or rejects if it times out or is cancelled
   */
  private _createAuthPromise(state: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this._pendingStates.set(state, { resolve, reject });
      
      // Set a timeout to clean up if the authentication doesn't complete
      setTimeout(() => {
        if (this._pendingStates.has(state)) {
          this._pendingStates.delete(state);
          
          // Reject with an error that indicates cancellation or timeout
          const error = new Error('Authentication timed out after 3 minutes');
          error.name = 'AuthenticationTimeoutError';
          
          reject(error);
          
          // Reset auth state to signed out
          this._setAuthState(AuthState.SignedOut);
          
          // Notify webviews about the cancellation
          this._notifyWebviewsOfAuthChange();
        }
      }, 3 * 60 * 1000); // 3 minutes timeout (reduced from 10 minutes)
    });
  }
  
  /**
   * Checks for an existing session on startup
   */
  private async _checkExistingSession(): Promise<void> {
    const isValid = await this.validateApiKey();
    
    if (isValid) {
      this._setAuthState(AuthState.SignedIn);
    } else {
      this._setAuthState(AuthState.SignedOut);
    }
  }
    /**
   * Updates the authentication state and emits an event
   */
  private _setAuthState(state: AuthState): void {
    if (this._authState !== state) {
      this._authState = state;
      this._authStateChangeEmitter.fire(state);
      
      // Notify webview panels of the auth state change
      this._notifyWebviewsOfAuthChange();
    }
  }
  
  /**
   * Notifies all active webviews of an authentication state change
   */
  private _notifyWebviewsOfAuthChange(): void {
    try {
      // Get the API key to include in the notification
      this._secretsService.getApiKey().then(key => {
        // Find all active webview panels
        const views = vscode.window.visibleTextEditors
          .filter(editor => editor.document.uri.scheme === 'vscode-webview')
          .map(editor => editor.document.uri);
        
        // Use VS Code's command system to broadcast the auth change
        vscode.commands.executeCommand('voicehype.notifyAuthChange', {
          authenticated: this._authState === AuthState.SignedIn,
          userProfile: this._userProfile,
          apiKey: key
        });
      }).catch(error => {
        console.error('Error getting API key for auth notification:', error);
      });
    } catch (error) {
      console.error('Error notifying webviews of auth change:', error);
    }
  }
  
  /**
   * Dispose of resources
   */
  /**
   * Clears all pending authentication states
   */
  public clearPendingStates(): void {
    this._pendingStates.clear();
    this._setAuthState(AuthState.SignedOut);
  }

  /**
   * Get user subscription data from Supabase
   */
  public async getSubscriptionData(): Promise<any> {
    if (!this._supabaseClient || this._authState !== AuthState.SignedIn) {
      throw new Error('Not authenticated');
    }

    try {
      const { data, error } = await this._supabaseClient
        .from('subscriptions')
        .select('*')
        .eq('user_id', this._userProfile?.id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to fetch subscription data:', error);
      throw error;
    }
  }

  /**
   * Get user credits data from Supabase
   */
  public async getCreditsData(): Promise<any> {
    if (!this._supabaseClient || this._authState !== AuthState.SignedIn) {
      throw new Error('Not authenticated');
    }

    try {
      const { data, error } = await this._supabaseClient
        .from('credits')
        .select('*')
        .eq('user_id', this._userProfile?.id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to fetch credits data:', error);
      throw error;
    }
  }

  /**
   * Get user usage data from Supabase
   */
  public async getUsageData(): Promise<any> {
    if (!this._supabaseClient || this._authState !== AuthState.SignedIn) {
      throw new Error('Not authenticated');
    }

    try {
      const { data, error } = await this._supabaseClient
        .from('usage_stats')
        .select('*')
        .eq('user_id', this._userProfile?.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to fetch usage data:', error);
      throw error;
    }
  }

  /**
   * Get recent activity from Supabase
   */
  public async getRecentActivity(limit: number = 10): Promise<any[]> {
    if (!this._supabaseClient || this._authState !== AuthState.SignedIn) {
      throw new Error('Not authenticated');
    }

    try {
      const { data, error } = await this._supabaseClient
        .from('activity_log')
        .select('*')
        .eq('user_id', this._userProfile?.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Failed to fetch recent activity:', error);
      throw error;
    }
  }

  /**
   * Sign out and clear API key
   */
  public async signOutAndClearApiKey(): Promise<void> {
    try {
      // Clear Supabase session
      if (this._supabaseClient) {
        await this._supabaseClient.auth.signOut();
      }

      // Clear API key from secrets
      await this._secretsService.deleteApiKey();

      // Reset state
      this._setAuthState(AuthState.SignedOut);
      this._userProfile = undefined;
      this._supabaseSession = undefined;

      console.log('VoiceHype: Successfully signed out and cleared API key');
    } catch (error) {
      console.error('VoiceHype: Error during sign out:', error);
      throw error;
    }
  }

  public dispose(): void {
    this._disposables.forEach(d => d.dispose());
    this._disposables = [];
    this._authStateChangeEmitter.dispose();
  }
}
